2025-05-28 10:05:12,870 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:05:12,870 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 10:05:12,876 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:05:12,891 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:05:12,891 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 10:05:12,892 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 10:05:12,892 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 10:05:43,579 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 10:05:59,119 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 10:05:59,120 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 10:06:00,707 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 10:06:01,709 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:06:06,454 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:06:07,455 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 10:06:08,990 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 10:06:09,992 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 10:06:12,184 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 10:06:13,185 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 10:06:19,026 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 10:06:20,029 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 10:06:20,029 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 10:06:20,029 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'other', 'company_official']}
2025-05-28 10:06:20,030 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 10:06:20,030 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 10:06:23,370 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:23,383 - src.openai_client - INFO - Extracted organization_name: CLP India Private Limited (confidence: 0.90)
2025-05-28 10:06:24,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:24,522 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.70)
2025-05-28 10:06:26,248 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:26,252 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 10:06:27,384 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:27,388 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 10:06:28,581 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:28,585 - src.openai_client - INFO - Extracted plants_count: 1 (confidence: 0.70)
2025-05-28 10:06:29,616 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:29,619 - src.openai_client - INFO - Extracted plant_types: coal (confidence: 0.70)
2025-05-28 10:06:30,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:30,926 - src.openai_client - INFO - Extracted ppa_flag: unknown (confidence: 0.00)
2025-05-28 10:06:32,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:32,241 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 10:06:33,496 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:33,500 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 10:06:34,004 - src.simple_pipeline - ERROR - ❌ Simplified extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:06:34,004 - root - ERROR - Extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_pipeline.py", line 86, in extract_jhajjar_openai
    org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)
                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/simple_pipeline.py", line 91, in extract_plant_data
    org_details = await self.enhanced_extractor.extract_adaptively(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/enhanced_extractor.py", line 365, in extract_adaptively
    return OrganizationalDetails(**extracted_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/pydantic/main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:11:10,486 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:11:10,487 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 10:11:10,492 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:11:10,503 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:11:10,503 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 10:11:10,503 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 10:11:10,503 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 10:11:14,063 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 10:11:14,063 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 10:11:15,568 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 10:11:16,570 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:11:17,603 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:11:18,604 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 10:11:25,365 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 10:11:26,367 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 10:11:28,991 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 10:11:29,992 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 10:11:35,215 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 10:11:36,219 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 10:11:36,220 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 10:11:36,220 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'company_official', 'other']}
2025-05-28 10:11:36,221 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 10:11:36,221 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 10:11:37,036 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:37,044 - src.openai_client - INFO - Extracted organization_name: "CLP India Private Limited" (confidence: 0.90)
2025-05-28 10:11:38,206 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:38,210 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.70)
2025-05-28 10:11:39,418 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:39,422 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 10:11:40,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:40,483 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 10:11:41,546 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:41,550 - src.openai_client - INFO - Extracted plants_count: 1 (confidence: 0.70)
2025-05-28 10:11:42,640 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:42,644 - src.openai_client - INFO - Extracted plant_types: coal (confidence: 0.70)
2025-05-28 10:11:44,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:44,006 - src.openai_client - INFO - Extracted ppa_flag: unknown (confidence: 0.00)
2025-05-28 10:11:45,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:45,096 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 10:11:46,347 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:46,355 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 10:11:46,859 - src.simple_pipeline - ERROR - ❌ Simplified extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:11:46,859 - root - ERROR - Extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_pipeline.py", line 86, in extract_jhajjar_openai
    org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)
                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/simple_pipeline.py", line 91, in extract_plant_data
    org_details = await self.enhanced_extractor.extract_adaptively(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/enhanced_extractor.py", line 368, in extract_adaptively
    return OrganizationalDetails(**extracted_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/pydantic/main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:15:03,726 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:15:03,727 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 10:15:03,732 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:15:03,743 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:15:03,743 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 10:15:03,743 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 10:15:03,743 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 10:15:17,303 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 10:15:17,304 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 10:15:18,337 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 10:15:19,339 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:15:24,409 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:15:25,411 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 10:15:27,287 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 10:15:28,288 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 10:15:29,781 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 10:15:30,783 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 10:15:32,489 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 10:15:33,491 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 10:15:33,491 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 10:15:33,491 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['company_official', 'other', 'wikipedia']}
2025-05-28 10:15:33,491 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 10:15:33,491 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 10:15:34,246 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:34,253 - src.openai_client - INFO - Extracted organization_name: "CLP India Private Limited" (confidence: 0.90)
2025-05-28 10:15:35,533 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:35,549 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.70)
2025-05-28 10:15:36,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:36,756 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 10:15:37,890 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:37,894 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 10:15:38,885 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:38,889 - src.openai_client - INFO - Extracted plants_count: 1 (confidence: 0.70)
2025-05-28 10:15:39,961 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:39,965 - src.openai_client - INFO - Extracted plant_types: coal (confidence: 0.70)
2025-05-28 10:15:41,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:41,480 - src.openai_client - INFO - Extracted ppa_flag: unknown (confidence: 0.00)
2025-05-28 10:15:42,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:42,982 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 10:15:44,390 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:44,394 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 10:15:44,898 - src.simple_pipeline - ERROR - ❌ Simplified extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:15:44,899 - root - ERROR - Extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_pipeline.py", line 86, in extract_jhajjar_openai
    org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)
                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/simple_pipeline.py", line 91, in extract_plant_data
    org_details = await self.enhanced_extractor.extract_adaptively(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/enhanced_extractor.py", line 371, in extract_adaptively
    return OrganizationalDetails(**extracted_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/pydantic/main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:34:00,613 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:34:00,613 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 10:34:00,619 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:34:00,631 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:34:00,631 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 10:34:00,631 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 10:34:00,631 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 10:34:15,602 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 10:34:15,603 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 10:34:24,216 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 10:34:25,217 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:34:25,801 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:34:26,802 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 10:34:29,283 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 10:34:30,285 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 10:34:32,298 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 10:34:33,300 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 10:34:35,272 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 10:34:36,274 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 10:34:36,274 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 10:34:36,275 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'other', 'company_official']}
2025-05-28 10:34:36,275 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 10:34:36,275 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 10:34:37,478 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:34:37,488 - src.openai_client - INFO - Extracted organization_name: CLP India Private Limited (confidence: 0.90)
2025-05-28 10:34:40,556 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:34:40,560 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.60)
2025-05-28 10:34:42,766 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:34:42,770 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 10:34:44,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:34:44,081 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 10:34:47,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:34:47,083 - src.openai_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-28 10:34:48,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:34:48,173 - src.openai_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-28 10:34:49,436 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:34:49,439 - src.openai_client - INFO - Extracted ppa_flag:  (confidence: 0.00)
2025-05-28 10:34:51,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:34:51,108 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 10:34:52,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:34:52,336 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 10:34:52,838 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-28 10:34:52,839 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-28 10:34:52,839 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 10:34:52,839 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 10:35:00,842 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: RetryError[<Future at 0x106a32330 state=finished raised TypeError>]
2025-05-28 10:35:00,842 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 10:35:09,348 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: RetryError[<Future at 0x106bcd490 state=finished raised TypeError>]
2025-05-28 10:35:09,348 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 10:35:17,854 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: RetryError[<Future at 0x106bceba0 state=finished raised TypeError>]
2025-05-28 10:35:17,855 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 10:35:26,361 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: RetryError[<Future at 0x106bdaab0 state=finished raised TypeError>]
2025-05-28 10:35:26,362 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 10:35:34,866 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: RetryError[<Future at 0x106bdb1d0 state=finished raised TypeError>]
2025-05-28 10:35:34,868 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 10:35:43,372 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: RetryError[<Future at 0x106bdad20 state=finished raised TypeError>]
2025-05-28 10:35:43,373 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 10:35:43,874 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 10:35:43,876 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "description"'
2025-05-28 10:35:43,876 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-28 10:35:43,876 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 10:35:43,876 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 10:35:43,877 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-28 10:35:43,877 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 10:35:43,877 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-28 10:35:43,877 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-28 10:35:43,877 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-28 10:35:43,877 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-28 10:35:43,878 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-28 10:35:43,878 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-28 10:35:43,878 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-28 10:35:43,878 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 6 missing fields
2025-05-28 10:35:43,878 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 10:35:43,878 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-28 10:36:14,610 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 10:36:49,614 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 10:37:22,506 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:37:23,660 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:37:24,662 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 10:37:25,530 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 10:37:26,531 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 10:37:27,247 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 10:37:28,255 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 10:37:36,259 - src.plant_details_extractor - ERROR - Error extracting single field lat: RetryError[<Future at 0x106ad5a60 state=finished raised TypeError>]
2025-05-28 10:37:36,261 - src.simple_pipeline - INFO - 📝 Content found for 'lat' but extraction failed (likely API limit)
2025-05-28 10:37:36,261 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 10:37:36,261 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
2025-05-28 10:37:41,929 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:37:43,497 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:37:44,499 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 10:37:45,123 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 10:37:46,124 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 10:37:47,157 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 10:37:48,159 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-28 10:37:56,162 - src.plant_details_extractor - ERROR - Error extracting single field long: RetryError[<Future at 0x106bcfec0 state=finished raised TypeError>]
2025-05-28 10:37:56,163 - src.simple_pipeline - INFO - 📝 Content found for 'long' but extraction failed (likely API limit)
2025-05-28 10:37:56,163 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-28 10:37:56,163 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant complete address location street city'
2025-05-28 10:38:08,844 - src.simple_pipeline - INFO - Scraping 1/2: https://www.bloomberg.com/profile/company/0808736D:IN
2025-05-28 10:38:17,022 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:38:17,022 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 10:38:17,028 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:38:17,039 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:38:17,039 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 10:38:17,039 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 10:38:17,039 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 10:39:44,414 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:39:44,414 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 10:39:44,420 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:39:44,436 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:39:44,436 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 10:39:44,436 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 10:39:44,436 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 10:40:14,609 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 10:40:49,610 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 10:41:23,620 - src.serp_client - ERROR - SERP API request failed: Cannot connect to host api.scraperapi.com:443 ssl:default [nodename nor servname provided, or not known]
2025-05-28 10:41:23,623 - src.simple_pipeline - ERROR - ❌ Simplified extraction failed for Jhajjar Power Plant: RetryError[<Future at 0x1077fb020 state=finished raised ClientConnectorError>]
2025-05-28 10:41:23,623 - root - ERROR - Extraction failed for Jhajjar Power Plant: RetryError[<Future at 0x1077fb020 state=finished raised ClientConnectorError>]
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/connector.py", line 1173, in _create_direct_connection
    hosts = await asyncio.shield(host_resolved)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/connector.py", line 884, in _resolve_host
    addrs = await self._resolver.resolve(host, port, family=self._family)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/resolver.py", line 33, in resolve
    infos = await self._loop.getaddrinfo(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/asyncio/base_events.py", line 905, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/socket.py", line 978, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/_asyncio.py", line 50, in __call__
    result = await fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/serp_client.py", line 65, in search
    async with self.session.get(self.base_url, params=params) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/client.py", line 1187, in __aenter__
    self._resp = await self._coro
                 ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/client.py", line 574, in _request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/connector.py", line 544, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/connector.py", line 911, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/connector.py", line 1187, in _create_direct_connection
    raise ClientConnectorError(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.scraperapi.com:443 ssl:default [nodename nor servname provided, or not known]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_pipeline.py", line 86, in extract_jhajjar_openai
    org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)
                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/simple_pipeline.py", line 68, in extract_plant_data
    search_results = await serp_client.search(plant_name, num_results=5)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/_asyncio.py", line 88, in async_wrapped
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/_asyncio.py", line 47, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/__init__.py", line 326, in iter
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x1077fb020 state=finished raised ClientConnectorError>]
2025-05-28 10:44:55,953 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:44:55,953 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 10:44:55,958 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:44:55,969 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:44:55,970 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 10:44:55,970 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 10:44:55,970 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 10:45:14,725 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 10:45:14,725 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 10:45:15,914 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 10:45:16,915 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:45:18,070 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:45:19,072 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 10:45:21,109 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 10:45:22,111 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 10:45:24,517 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 10:45:25,520 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 10:45:26,986 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 10:45:27,988 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 10:45:27,989 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 10:45:27,989 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['company_official', 'other', 'wikipedia']}
2025-05-28 10:45:27,989 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 10:45:27,989 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 10:45:30,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:45:30,954 - src.openai_client - INFO - Extracted organization_name: "CLP India Private Limited" (confidence: 0.90)
2025-05-28 10:45:32,499 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:45:32,504 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.60)
2025-05-28 10:45:33,713 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:45:33,753 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 10:45:35,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:45:35,808 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 10:45:36,838 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:45:36,839 - src.openai_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-28 10:45:38,665 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:45:38,669 - src.openai_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-28 10:45:40,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:45:40,166 - src.openai_client - INFO - Extracted ppa_flag:  (confidence: 0.00)
2025-05-28 10:45:46,579 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:45:46,584 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 10:45:49,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:45:49,689 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 10:45:50,192 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-28 10:45:50,192 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-28 10:45:50,193 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 10:45:50,193 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 10:45:58,197 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: RetryError[<Future at 0x107265bb0 state=finished raised TypeError>]
2025-05-28 10:45:58,198 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 10:46:06,701 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: RetryError[<Future at 0x107266c30 state=finished raised TypeError>]
2025-05-28 10:46:06,701 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 10:46:15,206 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: RetryError[<Future at 0x107264980 state=finished raised TypeError>]
2025-05-28 10:46:15,207 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 10:46:23,712 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: RetryError[<Future at 0x107271010 state=finished raised TypeError>]
2025-05-28 10:46:23,713 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 10:46:32,218 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: RetryError[<Future at 0x107273170 state=finished raised TypeError>]
2025-05-28 10:46:32,219 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 10:46:40,723 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: RetryError[<Future at 0x107273200 state=finished raised TypeError>]
2025-05-28 10:46:40,724 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 10:46:41,226 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 10:46:41,226 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "description"'
2025-05-28 10:46:41,227 - src.plant_details_extractor - INFO - Using organization name for plant name: "CLP India Private Limited"
2025-05-28 10:46:41,227 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 10:46:41,227 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 10:46:41,227 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-28 10:46:41,227 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 10:46:41,227 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-28 10:46:41,227 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-28 10:46:41,228 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-28 10:46:41,228 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-28 10:46:41,228 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-28 10:46:41,228 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-28 10:46:41,228 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-28 10:46:41,228 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 6 missing fields
2025-05-28 10:46:41,228 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 10:46:41,228 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-28 10:47:03,095 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:47:04,681 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:47:05,683 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 10:47:07,202 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 10:47:08,203 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 10:47:09,128 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 10:47:10,131 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 10:47:18,135 - src.plant_details_extractor - ERROR - Error extracting single field lat: RetryError[<Future at 0x106eff620 state=finished raised TypeError>]
2025-05-28 10:47:18,135 - src.simple_pipeline - INFO - 📝 Content found for 'lat' but extraction failed (likely API limit)
2025-05-28 10:47:18,136 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 10:47:18,136 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
2025-05-28 10:47:30,141 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:47:41,928 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:47:42,930 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 10:47:44,382 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 10:47:45,383 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 10:47:47,117 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 10:47:48,119 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-28 10:47:56,123 - src.plant_details_extractor - ERROR - Error extracting single field long: RetryError[<Future at 0x10722bc50 state=finished raised TypeError>]
2025-05-28 10:47:56,123 - src.simple_pipeline - INFO - 📝 Content found for 'long' but extraction failed (likely API limit)
2025-05-28 10:47:56,124 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-28 10:47:56,124 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant complete address location street city'
2025-05-28 10:48:00,114 - src.simple_pipeline - INFO - Scraping 1/2: https://www.bloomberg.com/profile/company/0808736D:IN
2025-05-28 10:48:04,285 - src.simple_pipeline - INFO - Successfully scraped https://www.bloomberg.com/profile/company/0808736D:IN (193 chars)
2025-05-28 10:48:05,286 - src.simple_pipeline - INFO - Scraping 2/2: https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-28 10:48:11,266 - src.scraper_client - WARNING - Failed to scrape https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html: HTTP 403
2025-05-28 10:48:11,267 - src.simple_pipeline - WARNING - Content too short or failed for https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-28 10:48:12,269 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 1 sources
2025-05-28 10:48:20,273 - src.plant_details_extractor - ERROR - Error extracting single field plant_address: RetryError[<Future at 0x10722b410 state=finished raised TypeError>]
2025-05-28 10:48:20,273 - src.simple_pipeline - INFO - 📝 Content found for 'plant_address' but extraction failed (likely API limit)
2025-05-28 10:48:20,273 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 10:48:20,274 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line'
2025-05-28 10:48:40,517 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 10:48:50,879 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 10:48:50,881 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-28 10:48:50,881 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-28 10:48:51,883 - src.simple_pipeline - INFO - Scraping 2/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 10:49:05,796 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 10:49:05,799 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-28 10:49:05,799 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-28 10:49:06,802 - src.simple_pipeline - INFO - Scraping 3/3: https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/
2025-05-28 10:49:09,120 - src.simple_pipeline - INFO - Successfully scraped https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/ (846 chars)
2025-05-28 10:49:10,122 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-28 10:49:10,123 - src.plant_details_extractor - ERROR - Error extracting single field grid_connectivity_maps: '\n  "description"'
2025-05-28 10:49:10,123 - src.simple_pipeline - INFO - 📝 Content found for 'grid_connectivity_maps' but extraction failed (likely API limit)
2025-05-28 10:49:10,124 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 10:49:10,124 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract details'
2025-05-28 10:49:14,254 - src.simple_pipeline - INFO - Scraping 1/3: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-28 10:49:22,089 - src.pdf_processor - INFO - Successfully extracted 36539 chars from PDF using pdfplumber: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-28 10:49:22,090 - src.scraper_client - INFO - Successfully processed PDF: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars, score: 1.00)
2025-05-28 10:49:22,090 - src.simple_pipeline - INFO - Successfully scraped https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars)
2025-05-28 10:49:23,092 - src.simple_pipeline - INFO - Scraping 2/3: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-28 10:49:30,583 - src.pdf_processor - INFO - Successfully extracted 17990 chars from PDF using pdfplumber: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-28 10:49:30,585 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars, score: 1.00)
2025-05-28 10:49:30,585 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars)
2025-05-28 10:49:31,586 - src.simple_pipeline - INFO - Scraping 3/3: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 10:49:38,362 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 10:49:38,364 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-28 10:49:38,364 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-28 10:49:39,366 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 3 sources
2025-05-28 10:49:39,366 - src.plant_details_extractor - ERROR - Error extracting single field ppa_details: '\n  "description"'
2025-05-28 10:49:39,367 - src.simple_pipeline - INFO - 📝 Content found for 'ppa_details' but extraction failed (likely API limit)
2025-05-28 10:49:39,367 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-28 10:49:39,367 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant units generators unit numbers'
2025-05-28 10:49:45,183 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 10:49:52,363 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 10:49:52,365 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-28 10:49:52,365 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-28 10:49:53,367 - src.simple_pipeline - INFO - Scraping 2/3: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 10:50:16,428 - src.pdf_processor - INFO - Successfully extracted 89455 chars from PDF using pdfplumber: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 10:50:16,429 - src.scraper_client - INFO - Successfully processed PDF: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars, score: 1.00)
2025-05-28 10:50:16,430 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars)
2025-05-28 10:50:17,431 - src.simple_pipeline - INFO - Scraping 3/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:50:18,455 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:50:19,457 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-28 10:50:27,461 - src.plant_details_extractor - ERROR - Error extracting single field units_id: RetryError[<Future at 0x16a8ccdd0 state=finished raised TypeError>]
2025-05-28 10:50:27,462 - src.simple_pipeline - INFO - 📝 Content found for 'units_id' but extraction failed (likely API limit)
2025-05-28 10:50:27,470 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250528_105027.json
2025-05-28 10:50:27,470 - src.simple_pipeline - INFO - ✅ Extracted grid connectivity information from targeted content
2025-05-28 10:50:27,471 - src.simple_pipeline - INFO - ✅ Extracted PPA details information from targeted content
2025-05-28 10:50:27,471 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-28 10:50:27,471 - src.simple_pipeline - INFO - ⏱️  Total time: 331.5s
2025-05-28 10:50:27,471 - src.simple_pipeline - INFO - 💾 Cache efficiency: 3 fields from cache, 6 targeted searches
2025-05-28 10:50:27,472 - src.simple_pipeline - INFO - 📊 Organizational results saved to jhajjar_org_openai_20250528_105027.json
2025-05-28 10:50:27,472 - src.simple_pipeline - INFO - 🔧 Plant details results saved to jhajjar_plant_openai_20250528_105027.json
2025-05-28 10:50:27,473 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_openai_20250528_105027.json
