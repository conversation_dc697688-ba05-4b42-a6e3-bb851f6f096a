2025-05-28 10:05:12,870 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:05:12,870 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 10:05:12,876 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:05:12,891 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:05:12,891 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 10:05:12,892 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 10:05:12,892 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 10:05:43,579 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 10:05:59,119 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 10:05:59,120 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 10:06:00,707 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 10:06:01,709 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:06:06,454 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:06:07,455 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 10:06:08,990 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 10:06:09,992 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 10:06:12,184 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 10:06:13,185 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 10:06:19,026 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 10:06:20,029 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 10:06:20,029 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 10:06:20,029 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'other', 'company_official']}
2025-05-28 10:06:20,030 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 10:06:20,030 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 10:06:23,370 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:23,383 - src.openai_client - INFO - Extracted organization_name: CLP India Private Limited (confidence: 0.90)
2025-05-28 10:06:24,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:24,522 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.70)
2025-05-28 10:06:26,248 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:26,252 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 10:06:27,384 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:27,388 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 10:06:28,581 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:28,585 - src.openai_client - INFO - Extracted plants_count: 1 (confidence: 0.70)
2025-05-28 10:06:29,616 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:29,619 - src.openai_client - INFO - Extracted plant_types: coal (confidence: 0.70)
2025-05-28 10:06:30,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:30,926 - src.openai_client - INFO - Extracted ppa_flag: unknown (confidence: 0.00)
2025-05-28 10:06:32,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:32,241 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 10:06:33,496 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:06:33,500 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 10:06:34,004 - src.simple_pipeline - ERROR - ❌ Simplified extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:06:34,004 - root - ERROR - Extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_pipeline.py", line 86, in extract_jhajjar_openai
    org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)
                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/simple_pipeline.py", line 91, in extract_plant_data
    org_details = await self.enhanced_extractor.extract_adaptively(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/enhanced_extractor.py", line 365, in extract_adaptively
    return OrganizationalDetails(**extracted_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/pydantic/main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:11:10,486 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:11:10,487 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 10:11:10,492 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:11:10,503 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:11:10,503 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 10:11:10,503 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 10:11:10,503 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 10:11:14,063 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 10:11:14,063 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 10:11:15,568 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 10:11:16,570 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:11:17,603 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:11:18,604 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 10:11:25,365 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 10:11:26,367 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 10:11:28,991 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 10:11:29,992 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 10:11:35,215 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 10:11:36,219 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 10:11:36,220 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 10:11:36,220 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'company_official', 'other']}
2025-05-28 10:11:36,221 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 10:11:36,221 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 10:11:37,036 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:37,044 - src.openai_client - INFO - Extracted organization_name: "CLP India Private Limited" (confidence: 0.90)
2025-05-28 10:11:38,206 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:38,210 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.70)
2025-05-28 10:11:39,418 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:39,422 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 10:11:40,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:40,483 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 10:11:41,546 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:41,550 - src.openai_client - INFO - Extracted plants_count: 1 (confidence: 0.70)
2025-05-28 10:11:42,640 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:42,644 - src.openai_client - INFO - Extracted plant_types: coal (confidence: 0.70)
2025-05-28 10:11:44,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:44,006 - src.openai_client - INFO - Extracted ppa_flag: unknown (confidence: 0.00)
2025-05-28 10:11:45,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:45,096 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 10:11:46,347 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:11:46,355 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 10:11:46,859 - src.simple_pipeline - ERROR - ❌ Simplified extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:11:46,859 - root - ERROR - Extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_pipeline.py", line 86, in extract_jhajjar_openai
    org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)
                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/simple_pipeline.py", line 91, in extract_plant_data
    org_details = await self.enhanced_extractor.extract_adaptively(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/enhanced_extractor.py", line 368, in extract_adaptively
    return OrganizationalDetails(**extracted_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/pydantic/main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:15:03,726 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:15:03,727 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 10:15:03,732 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:15:03,743 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 10:15:03,743 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 10:15:03,743 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 10:15:03,743 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 10:15:17,303 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 10:15:17,304 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 10:15:18,337 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 10:15:19,339 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 10:15:24,409 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 10:15:25,411 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 10:15:27,287 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 10:15:28,288 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 10:15:29,781 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 10:15:30,783 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 10:15:32,489 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 10:15:33,491 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 10:15:33,491 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 10:15:33,491 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['company_official', 'other', 'wikipedia']}
2025-05-28 10:15:33,491 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 10:15:33,491 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 10:15:34,246 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:34,253 - src.openai_client - INFO - Extracted organization_name: "CLP India Private Limited" (confidence: 0.90)
2025-05-28 10:15:35,533 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:35,549 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.70)
2025-05-28 10:15:36,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:36,756 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 10:15:37,890 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:37,894 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 10:15:38,885 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:38,889 - src.openai_client - INFO - Extracted plants_count: 1 (confidence: 0.70)
2025-05-28 10:15:39,961 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:39,965 - src.openai_client - INFO - Extracted plant_types: coal (confidence: 0.70)
2025-05-28 10:15:41,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:41,480 - src.openai_client - INFO - Extracted ppa_flag: unknown (confidence: 0.00)
2025-05-28 10:15:42,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:42,982 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 10:15:44,390 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 10:15:44,394 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 10:15:44,898 - src.simple_pipeline - ERROR - ❌ Simplified extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-05-28 10:15:44,899 - root - ERROR - Extraction failed for Jhajjar Power Plant: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_pipeline.py", line 86, in extract_jhajjar_openai
    org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)
                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/simple_pipeline.py", line 91, in extract_plant_data
    org_details = await self.enhanced_extractor.extract_adaptively(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/enhanced_extractor.py", line 371, in extract_adaptively
    return OrganizationalDetails(**extracted_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/pydantic/main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for OrganizationalDetails
plant_types
  Input should be a valid list [type=list_type, input_value='coal', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
